// 用户相关类型
export interface User {
  id: string;
  email: string;
  phone?: string;
  nickname: string;
  avatar?: string;
  stylePreferences?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 衣物分类
export interface ClothingCategory {
  id: string;
  name: string;
  icon: string;
  isCustom: boolean;
  order: number;
}

// 衣物标签
export interface ClothingTag {
  id: string;
  name: string;
  type: 'season' | 'style' | 'material' | 'color' | 'custom';
  color?: string;
}

// 衣物单品
export interface ClothingItem {
  id: string;
  userId: string;
  categoryId: string;
  name?: string;
  description?: string;
  images: string[];
  tags: ClothingTag[];
  price?: number | string; // Support both number and string for display
  brand?: string;
  size?: string;
  color?: string;
  material?: string;
  season?: string;
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// AI推荐场景
export interface RecommendationScene {
  id: string;
  name: string;
  icon: string;
  description: string;
}

// 天气信息
export interface WeatherInfo {
  temperature: number;
  condition: string;
  humidity: number;
  city: string;
}

// AI推荐搭配
export interface Recommendation {
  id: string;
  userId: string;
  items: ClothingItem[];
  scene: RecommendationScene;
  weather?: WeatherInfo;
  createdAt: Date;
  isFavorite: boolean;
}

// 导航参数类型
export type RootStackParamList = {
  Splash: undefined;
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  ResetPassword: undefined;
  ItemDetail: { itemId: string };
  Upload: undefined;
  AIRecommendation: { itemId?: string };
  RecommendationDetail: { recommendationId: string };
  ItemSource: { recommendationId: string };
};

export type MainTabParamList = {
  Wardrobe: undefined;
  Favorites: undefined;
  Upload: undefined;
  AIRecommendation: undefined;
  Profile: undefined;
};

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页数据
export interface PaginatedData<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
