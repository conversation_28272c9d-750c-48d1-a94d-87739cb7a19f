// iOS Design System
// 基于Apple Human Interface Guidelines

export const iOSColors = {
  // iOS系统颜色
  systemBlue: '#007AFF',
  systemGreen: '#34C759',
  systemIndigo: '#5856D6',
  systemOrange: '#FF9500',
  systemPink: '#FF2D92',
  systemPurple: '#AF52DE',
  systemRed: '#FF3B30',
  systemTeal: '#5AC8FA',
  systemYellow: '#FFCC00',

  // 灰度系统
  systemGray: '#8E8E93',
  systemGray2: '#AEAEB2',
  systemGray3: '#C7C7CC',
  systemGray4: '#D1D1D6',
  systemGray5: '#E5E5EA',
  systemGray6: '#F2F2F7',

  // 语义化颜色
  label: '#000000',
  secondaryLabel: '#3C3C43',
  tertiaryLabel: '#3C3C43',
  quaternaryLabel: '#3C3C43',

  // 背景色
  systemBackground: '#FFFFFF',
  secondarySystemBackground: '#F2F2F7',
  tertiarySystemBackground: '#FFFFFF',

  // 分组背景色
  systemGroupedBackground: '#F2F2F7',
  secondarySystemGroupedBackground: '#FFFFFF',
  tertiarySystemGroupedBackground: '#F2F2F7',

  // 分隔线
  separator: '#3C3C43',
  opaqueSeparator: '#C6C6C8',

  // 链接色
  link: '#007AFF',

  // 深色模式颜色
  dark: {
    label: '#FFFFFF',
    secondaryLabel: '#EBEBF5',
    tertiaryLabel: '#EBEBF5',
    systemBackground: '#000000',
    secondarySystemBackground: '#1C1C1E',
    tertiarySystemBackground: '#2C2C2E',
    systemGroupedBackground: '#000000',
    secondarySystemGroupedBackground: '#1C1C1E',
    separator: '#EBEBF5',
  }
};

export const iOSTypography = {
  // iOS字体系统
  largeTitle: {
    fontSize: 34,
    fontWeight: '400' as const,
    lineHeight: 41,
    letterSpacing: 0.37,
  },
  title1: {
    fontSize: 28,
    fontWeight: '400' as const,
    lineHeight: 34,
    letterSpacing: 0.36,
  },
  title2: {
    fontSize: 22,
    fontWeight: '400' as const,
    lineHeight: 28,
    letterSpacing: 0.35,
  },
  title3: {
    fontSize: 20,
    fontWeight: '400' as const,
    lineHeight: 25,
    letterSpacing: 0.38,
  },
  headline: {
    fontSize: 17,
    fontWeight: '600' as const,
    lineHeight: 22,
    letterSpacing: -0.41,
  },
  body: {
    fontSize: 17,
    fontWeight: '400' as const,
    lineHeight: 22,
    letterSpacing: -0.41,
  },
  callout: {
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 21,
    letterSpacing: -0.32,
  },
  subheadline: {
    fontSize: 15,
    fontWeight: '400' as const,
    lineHeight: 20,
    letterSpacing: -0.24,
  },
  footnote: {
    fontSize: 13,
    fontWeight: '400' as const,
    lineHeight: 18,
    letterSpacing: -0.08,
  },
  caption1: {
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 16,
    letterSpacing: 0,
  },
  caption2: {
    fontSize: 11,
    fontWeight: '400' as const,
    lineHeight: 13,
    letterSpacing: 0.07,
  },
};

export const iOSSpacing = {
  // iOS标准间距
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,

  // 特定用途间距
  cardPadding: 16,
  sectionSpacing: 24,
  listItemHeight: 44,
  navigationBarHeight: 44,
  tabBarHeight: 49,
  statusBarHeight: 44,
};

export const iOSBorderRadius = {
  // iOS圆角系统
  small: 8,
  medium: 12,
  large: 16,
  xlarge: 20,
  
  // 特定组件圆角
  button: 12,
  card: 16,
  modal: 20,
  avatar: 999, // 完全圆形
};

export const iOSShadows = {
  // iOS阴影系统
  small: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  large: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  card: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
  },
};

export const iOSAnimations = {
  // iOS动画时长
  fast: 200,
  normal: 300,
  slow: 500,
  
  // 缓动函数
  easing: {
    easeInOut: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
    easeOut: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0.0, 1, 1)',
    sharp: 'cubic-bezier(0.4, 0.0, 0.6, 1)',
  },
};

// 组件样式预设
export const iOSComponentStyles = {
  // 按钮样式
  primaryButton: {
    backgroundColor: iOSColors.systemBlue,
    borderRadius: iOSBorderRadius.button,
    paddingVertical: 12,
    paddingHorizontal: 24,
    minHeight: 44,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    ...iOSShadows.small,
  },
  
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: iOSColors.systemBlue,
    borderRadius: iOSBorderRadius.button,
    paddingVertical: 12,
    paddingHorizontal: 24,
    minHeight: 44,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },

  // 卡片样式
  card: {
    backgroundColor: iOSColors.secondarySystemGroupedBackground,
    borderRadius: iOSBorderRadius.card,
    padding: iOSSpacing.cardPadding,
    ...iOSShadows.card,
  },

  // 输入框样式
  textInput: {
    backgroundColor: iOSColors.tertiarySystemBackground,
    borderRadius: iOSBorderRadius.medium,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 17,
    minHeight: 44,
    borderWidth: 1,
    borderColor: iOSColors.systemGray5,
  },

  // 列表项样式
  listItem: {
    backgroundColor: iOSColors.secondarySystemGroupedBackground,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minHeight: iOSSpacing.listItemHeight,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
  },

  // 导航栏样式
  navigationBar: {
    backgroundColor: iOSColors.systemBackground,
    height: iOSSpacing.navigationBarHeight,
    borderBottomWidth: 0.5,
    borderBottomColor: iOSColors.separator,
    ...iOSShadows.small,
  },
};

// 主题配置
export const iOSTheme = {
  colors: iOSColors,
  typography: iOSTypography,
  spacing: iOSSpacing,
  borderRadius: iOSBorderRadius,
  shadows: iOSShadows,
  animations: iOSAnimations,
  components: iOSComponentStyles,
};

export default iOSTheme;
