// Minimalist Modern Fashion UI Design System
// Based on the provided design system profile

export const FashionColors = {
  // Core Colors - Based on Figma Design
  background: '#FFFFFF',
  primaryText: '#000000',        // Pure black for product names
  secondaryText: '#666666',      // Medium gray for descriptions
  lightText: '#999999',          // Light gray for secondary info
  accent: '#000000',             // Black for active states
  highlight: '#FF4757',          // Red for favorites/hearts
  price: '#000000',              // Black for prices

  // CTA Colors
  ctaBackground: '#000000',      // Black primary buttons
  ctaText: '#FFFFFF',            // White text on black buttons
  secondaryCtaBackground: '#FFFFFF',
  secondaryCtaText: '#000000',
  secondaryCtaBorder: '#000000',

  // Surface Colors
  surface: '#FFFFFF',
  surfaceVariant: '#F8F8F8',     // Very light gray
  cardBackground: '#FFFFFF',

  // Border and Divider
  border: '#E8E8E8',             // Very light border
  divider: '#F0F0F0',
  shadow: '#000000',

  // Category/Tag Colors
  categoryActive: '#000000',
  categoryInactive: '#CCCCCC',
  categoryBackground: '#F5F5F5',

  // Status Colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#FF4757',
  info: '#2196F3',

  // Navigation Colors
  navbarBackground: '#FFFFFF',
  navbarActive: '#000000',
  navbarInactive: '#CCCCCC',
};

export const FashionTypography = {
  fontFamily: 'SF Pro Display', // iOS default, fallback to system
  
  title: {
    fontSize: 24,
    fontWeight: '600' as const,
    lineHeight: 32,
    color: FashionColors.primaryText,
  },
  
  subtitle: {
    fontSize: 18,
    fontWeight: '500' as const,
    lineHeight: 24,
    color: FashionColors.primaryText,
  },
  
  body: {
    fontSize: 14,
    fontWeight: '400' as const,
    lineHeight: 20,
    color: FashionColors.primaryText,
  },
  
  caption: {
    fontSize: 12,
    fontWeight: '300' as const,
    lineHeight: 16,
    color: FashionColors.secondaryText,
  },
  
  price: {
    fontSize: 16,
    fontWeight: '600' as const,
    lineHeight: 22,
    color: FashionColors.price,
  },
  
  button: {
    fontSize: 14,
    fontWeight: '500' as const,
    lineHeight: 20,
  },
  
  tag: {
    fontSize: 10,
    fontWeight: '400' as const,
    lineHeight: 14,
    color: FashionColors.tagText,
  },
};

export const FashionSpacing = {
  // Grid System (8px base unit)
  gridUnit: 8,

  // Standard Spacing - More generous for luxury feel
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,

  // Component Specific
  sectionMargin: 32,           // More space between sections
  cardPadding: 16,
  cardSpacing: 16,             // Space between cards
  componentSpacing: 16,

  // Layout - Based on Figma design
  screenPadding: 20,           // More generous screen padding
  screenPaddingHorizontal: 20,
  screenPaddingVertical: 24,
  listItemSpacing: 16,

  // Product specific
  productCardSpacing: 12,      // Space between product cards
  productImageSpacing: 8,      // Space around product images
};

export const FashionBorderRadius = {
  // Standard Radius
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  
  // Component Specific
  card: 12,
  button: 24, // Rounded buttons
  tag: 12, // Pill shape
  image: 8,
  modal: 24,
  
  // Special
  circle: 999,
};

export const FashionShadows = {
  // Soft shadows for modern look
  light: {
    shadowColor: FashionColors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  
  soft: {
    shadowColor: FashionColors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  
  medium: {
    shadowColor: FashionColors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  
  card: {
    shadowColor: FashionColors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 6,
    elevation: 3,
  },
};

export const FashionComponents = {
  // Category Navigation (Top)
  categoryNav: {
    backgroundColor: FashionColors.background,
    paddingHorizontal: FashionSpacing.screenPaddingHorizontal,
    paddingVertical: FashionSpacing.md,
    borderBottomWidth: 1,
    borderBottomColor: FashionColors.border,
  },

  // Category Tab
  categoryTab: {
    paddingHorizontal: FashionSpacing.lg,
    paddingVertical: FashionSpacing.sm,
    borderRadius: FashionBorderRadius.lg,
    marginRight: FashionSpacing.md,
  },

  // Product Card - Based on Figma design
  productCard: {
    backgroundColor: FashionColors.cardBackground,
    borderRadius: FashionBorderRadius.card,
    overflow: 'hidden' as const,
    marginBottom: FashionSpacing.productCardSpacing,
    // No shadow for cleaner look like Figma
  },

  // Product Image Container
  productImageContainer: {
    aspectRatio: 0.8, // 4:5 ratio like in Figma
    backgroundColor: FashionColors.surfaceVariant,
    borderRadius: FashionBorderRadius.md,
    overflow: 'hidden' as const,
  },
  
  // Button Variants - Based on Figma design
  primaryButton: {
    backgroundColor: FashionColors.ctaBackground,
    borderRadius: FashionBorderRadius.button,
    paddingVertical: 16,
    paddingHorizontal: 32,
    minHeight: 52,
    // No shadow for cleaner look
  },

  secondaryButton: {
    backgroundColor: FashionColors.secondaryCtaBackground,
    borderWidth: 1,
    borderColor: FashionColors.secondaryCtaBorder,
    borderRadius: FashionBorderRadius.button,
    paddingVertical: 16,
    paddingHorizontal: 32,
    minHeight: 52,
  },

  // Add to Cart Button (from Figma)
  addToCartButton: {
    backgroundColor: FashionColors.ctaBackground,
    borderRadius: FashionBorderRadius.button,
    paddingVertical: 18,
    paddingHorizontal: 40,
    minHeight: 56,
    width: '100%',
  },

  // Color Selector (from Figma product detail)
  colorSelector: {
    width: 32,
    height: 32,
    borderRadius: FashionBorderRadius.circle,
    borderWidth: 2,
    borderColor: 'transparent',
    marginRight: FashionSpacing.sm,
  },

  colorSelectorSelected: {
    borderColor: FashionColors.accent,
  },

  // Size Selector
  sizeSelector: {
    paddingVertical: FashionSpacing.sm,
    paddingHorizontal: FashionSpacing.md,
    borderRadius: FashionBorderRadius.sm,
    borderWidth: 1,
    borderColor: FashionColors.border,
    marginRight: FashionSpacing.sm,
    minWidth: 44,
    alignItems: 'center' as const,
  },

  sizeSelectorSelected: {
    backgroundColor: FashionColors.accent,
    borderColor: FashionColors.accent,
  },
  
  // Tag Component
  tag: {
    backgroundColor: FashionColors.tagBackground,
    borderRadius: FashionBorderRadius.tag,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  
  // Floating Action Button
  fab: {
    width: 48,
    height: 48,
    borderRadius: FashionBorderRadius.circle,
    backgroundColor: FashionColors.accent,
    position: 'absolute' as const,
    bottom: 24,
    right: 24,
    ...FashionShadows.medium,
  },
  
  // Image Styles
  productImage: {
    aspectRatio: 4/5, // 4:5 ratio as specified
    borderRadius: FashionBorderRadius.image,
    backgroundColor: FashionColors.surfaceVariant,
  },
  
  // Modal Sheet
  modalSheet: {
    backgroundColor: FashionColors.background,
    borderTopLeftRadius: FashionBorderRadius.modal,
    borderTopRightRadius: FashionBorderRadius.modal,
    minHeight: '50%',
    maxHeight: '80%',
  },
};

// Layout Presets
export const FashionLayouts = {
  // Grid Layout for Product Cards
  productGrid: {
    numColumns: 2,
    columnGap: FashionSpacing.md,
    rowGap: FashionSpacing.md,
    padding: FashionSpacing.screenPadding,
  },
  
  // List Layout
  list: {
    padding: FashionSpacing.screenPadding,
    gap: FashionSpacing.listItemSpacing,
  },
  
  // Section Layout
  section: {
    marginBottom: FashionSpacing.sectionMargin,
    paddingHorizontal: FashionSpacing.screenPadding,
  },
};

// Complete Design System Export
export const FashionDesignSystem = {
  colors: FashionColors,
  typography: FashionTypography,
  spacing: FashionSpacing,
  borderRadius: FashionBorderRadius,
  shadows: FashionShadows,
  components: FashionComponents,
  layouts: FashionLayouts,
};

export default FashionDesignSystem;
