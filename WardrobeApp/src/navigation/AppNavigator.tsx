import React from 'react';
import { useColorScheme } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { PaperProvider } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, MainTabParamList } from '../types';
import { lightTheme, darkTheme } from '../utils/theme';
import { FashionDesignSystem } from '../styles/FashionDesignSystem';

// 导入屏幕组件
import SplashScreen from '../screens/auth/SplashScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import WardrobeScreen from '../screens/wardrobe/WardrobeScreen';
import FavoritesScreen from '../screens/favorites/FavoritesScreen';
import UploadScreen from '../screens/upload/UploadScreen';
import AIRecommendationScreen from '../screens/ai/AIRecommendationScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import ItemDetailScreen from '../screens/wardrobe/ItemDetailScreen';
import ProductDetailScreen from '../screens/wardrobe/ProductDetailScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// 底部标签导航
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Wardrobe':
              iconName = 'checkroom';
              break;
            case 'Favorites':
              iconName = focused ? 'favorite' : 'favorite-border';
              break;
            case 'Upload':
              iconName = 'add-circle';
              break;
            case 'AIRecommendation':
              iconName = 'auto-awesome';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: FashionDesignSystem.colors.navbarActive,
        tabBarInactiveTintColor: FashionDesignSystem.colors.navbarInactive,
        tabBarStyle: {
          backgroundColor: FashionDesignSystem.colors.navbarBackground,
          borderTopWidth: 0,
          elevation: 8,
          shadowColor: FashionDesignSystem.colors.shadow,
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 10,
          fontWeight: '500',
          marginTop: 4,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Wardrobe" 
        component={WardrobeScreen}
        options={{ tabBarLabel: '我的衣柜' }}
      />
      <Tab.Screen 
        name="Favorites" 
        component={FavoritesScreen}
        options={{ tabBarLabel: '收藏夹' }}
      />
      <Tab.Screen 
        name="Upload" 
        component={UploadScreen}
        options={{ 
          tabBarLabel: '上传',
          tabBarIconStyle: { transform: [{ scale: 1.2 }] }
        }}
      />
      <Tab.Screen 
        name="AIRecommendation" 
        component={AIRecommendationScreen}
        options={{ tabBarLabel: 'AI推荐' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: '个人中心' }}
      />
    </Tab.Navigator>
  );
}

// 主导航器
function AppNavigator() {
  const colorScheme = useColorScheme();
  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Splash"
          screenOptions={{ headerShown: false }}
        >
          <Stack.Screen name="Splash" component={SplashScreen} />
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
          <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
          <Stack.Screen name="Main" component={MainTabNavigator} />
          <Stack.Screen name="ItemDetail" component={ItemDetailScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    </PaperProvider>
  );
}

export default AppNavigator;
