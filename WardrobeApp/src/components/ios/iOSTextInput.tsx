import React, { useState } from 'react';
import { View, TextInput, Text, StyleSheet, ViewStyle, TextStyle, TextInputProps } from 'react-native';
import { iOSTheme } from '../../styles/iOSDesignSystem';

interface iOSTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'rounded' | 'underlined';
  size?: 'small' | 'medium' | 'large';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
}

const iOSTextInput: React.FC<iOSTextInputProps> = ({
  label,
  error,
  helperText,
  variant = 'default',
  size = 'medium',
  leftIcon,
  rightIcon,
  containerStyle,
  inputStyle,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      marginBottom: iOSTheme.spacing.sm,
    };

    return baseStyle;
  };

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: iOSTheme.colors.tertiarySystemBackground,
    };

    const sizeStyles = {
      small: {
        minHeight: 36,
        paddingHorizontal: 12,
      },
      medium: {
        minHeight: 44,
        paddingHorizontal: 16,
      },
      large: {
        minHeight: 52,
        paddingHorizontal: 20,
      },
    };

    const variantStyles = {
      default: {
        borderRadius: iOSTheme.borderRadius.medium,
        borderWidth: 1,
        borderColor: error 
          ? iOSTheme.colors.systemRed 
          : isFocused 
            ? iOSTheme.colors.systemBlue 
            : iOSTheme.colors.systemGray5,
      },
      rounded: {
        borderRadius: 22,
        borderWidth: 1,
        borderColor: error 
          ? iOSTheme.colors.systemRed 
          : isFocused 
            ? iOSTheme.colors.systemBlue 
            : iOSTheme.colors.systemGray5,
      },
      underlined: {
        borderRadius: 0,
        borderBottomWidth: 1,
        borderBottomColor: error 
          ? iOSTheme.colors.systemRed 
          : isFocused 
            ? iOSTheme.colors.systemBlue 
            : iOSTheme.colors.systemGray4,
        backgroundColor: 'transparent',
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getInputStyle = (): TextStyle => {
    const sizeStyles = {
      small: {
        ...iOSTheme.typography.footnote,
      },
      medium: {
        ...iOSTheme.typography.body,
      },
      large: {
        ...iOSTheme.typography.headline,
      },
    };

    return {
      flex: 1,
      color: iOSTheme.colors.label,
      ...sizeStyles[size],
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      ...iOSTheme.typography.subheadline,
      color: iOSTheme.colors.secondaryLabel,
      marginBottom: iOSTheme.spacing.xs,
      fontWeight: '500',
    };
  };

  const getHelperTextStyle = (): TextStyle => {
    return {
      ...iOSTheme.typography.caption1,
      color: error ? iOSTheme.colors.systemRed : iOSTheme.colors.secondaryLabel,
      marginTop: iOSTheme.spacing.xs,
      marginLeft: variant === 'underlined' ? 0 : iOSTheme.spacing.sm,
    };
  };

  return (
    <View style={[getContainerStyle(), containerStyle]}>
      {label && (
        <Text style={getLabelStyle()}>{label}</Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {leftIcon && (
          <View style={{ marginRight: iOSTheme.spacing.sm }}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          style={[getInputStyle(), inputStyle]}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholderTextColor={iOSTheme.colors.systemGray}
          selectionColor={iOSTheme.colors.systemBlue}
          {...textInputProps}
        />
        
        {rightIcon && (
          <View style={{ marginLeft: iOSTheme.spacing.sm }}>
            {rightIcon}
          </View>
        )}
      </View>
      
      {(error || helperText) && (
        <Text style={getHelperTextStyle()}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

export default iOSTextInput;
