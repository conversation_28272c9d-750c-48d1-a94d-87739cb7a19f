import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { iOSTheme } from '../../styles/iOSDesignSystem';

interface iOSCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'grouped' | 'inset';
  padding?: 'none' | 'small' | 'medium' | 'large';
}

const iOSCard: React.FC<iOSCardProps> = ({
  children,
  style,
  variant = 'default',
  padding = 'medium',
}) => {
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: iOSTheme.borderRadius.card,
      overflow: 'hidden',
    };

    const variantStyles = {
      default: {
        backgroundColor: iOSTheme.colors.secondarySystemGroupedBackground,
        ...iOSTheme.shadows.card,
      },
      grouped: {
        backgroundColor: iOSTheme.colors.secondarySystemGroupedBackground,
        marginHorizontal: iOSTheme.spacing.md,
        ...iOSTheme.shadows.card,
      },
      inset: {
        backgroundColor: iOSTheme.colors.tertiarySystemBackground,
        borderWidth: 1,
        borderColor: iOSTheme.colors.systemGray5,
      },
    };

    const paddingStyles = {
      none: { padding: 0 },
      small: { padding: iOSTheme.spacing.sm },
      medium: { padding: iOSTheme.spacing.md },
      large: { padding: iOSTheme.spacing.lg },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
      ...paddingStyles[padding],
    };
  };

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
};

export default iOSCard;
