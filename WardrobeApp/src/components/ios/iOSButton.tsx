import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, ActivityIndicator } from 'react-native';
import { iOSTheme } from '../../styles/iOSDesignSystem';

interface iOSButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'destructive' | 'plain';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

const iOSButton: React.FC<iOSButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
}) => {
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: iOSTheme.borderRadius.button,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    };

    // 尺寸样式
    const sizeStyles = {
      small: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        minHeight: 32,
      },
      medium: {
        paddingVertical: 12,
        paddingHorizontal: 24,
        minHeight: 44,
      },
      large: {
        paddingVertical: 16,
        paddingHorizontal: 32,
        minHeight: 52,
      },
    };

    // 变体样式
    const variantStyles = {
      primary: {
        backgroundColor: disabled ? iOSTheme.colors.systemGray4 : iOSTheme.colors.systemBlue,
        ...iOSTheme.shadows.small,
      },
      secondary: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: disabled ? iOSTheme.colors.systemGray4 : iOSTheme.colors.systemBlue,
      },
      destructive: {
        backgroundColor: disabled ? iOSTheme.colors.systemGray4 : iOSTheme.colors.systemRed,
        ...iOSTheme.shadows.small,
      },
      plain: {
        backgroundColor: 'transparent',
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...(fullWidth && { width: '100%' }),
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getTextStyle = (): TextStyle => {
    const sizeStyles = {
      small: iOSTheme.typography.footnote,
      medium: iOSTheme.typography.body,
      large: iOSTheme.typography.headline,
    };

    const variantStyles = {
      primary: {
        color: 'white',
        fontWeight: '600' as const,
      },
      secondary: {
        color: disabled ? iOSTheme.colors.systemGray : iOSTheme.colors.systemBlue,
        fontWeight: '600' as const,
      },
      destructive: {
        color: 'white',
        fontWeight: '600' as const,
      },
      plain: {
        color: disabled ? iOSTheme.colors.systemGray : iOSTheme.colors.systemBlue,
        fontWeight: '400' as const,
      },
    };

    return {
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' || variant === 'destructive' ? 'white' : iOSTheme.colors.systemBlue}
          style={{ marginRight: 8 }}
        />
      )}
      <Text style={[getTextStyle(), textStyle]}>
        {title}
      </Text>
    </TouchableOpacity>
  );
};

export default iOSButton;
