import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

interface Category {
  id: string;
  name: string;
  count?: number;
}

interface CategoryTabsProps {
  categories: Category[];
  activeCategory: string;
  onCategoryChange: (categoryId: string) => void;
  style?: any;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  categories,
  activeCategory,
  onCategoryChange,
  style
}) => {
  return (
    <View style={[styles.container, style]}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map((category) => {
          const isActive = activeCategory === category.id;
          return (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.tab,
                isActive && styles.activeTab
              ]}
              onPress={() => onCategoryChange(category.id)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.tabText,
                isActive && styles.activeTabText
              ]}>
                {category.name}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...FashionDesignSystem.components.categoryNav,
  },
  
  scrollContent: {
    paddingRight: FashionDesignSystem.spacing.screenPaddingHorizontal,
  },
  
  tab: {
    ...FashionDesignSystem.components.categoryTab,
    backgroundColor: 'transparent',
  },
  
  activeTab: {
    backgroundColor: FashionDesignSystem.colors.categoryActive,
  },
  
  tabText: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.categoryInactive,
    fontWeight: '400',
    fontSize: 15,
  },
  
  activeTabText: {
    color: FashionDesignSystem.colors.background,
    fontWeight: '500',
  },
});

export default CategoryTabs;
