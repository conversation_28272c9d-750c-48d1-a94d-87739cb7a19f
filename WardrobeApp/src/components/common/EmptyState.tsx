import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

interface EmptyStateProps {
  icon?: string;
  title: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'inbox',
  title,
  description,
  actionText,
  onAction
}) => {
  return (
    <View style={styles.container}>
      <Icon name={icon} size={64} color={FashionDesignSystem.colors.secondaryText} style={styles.icon} />
      <Text style={styles.title}>{title}</Text>
      {description && <Text style={styles.description}>{description}</Text>}
      {actionText && onAction && (
        <TouchableOpacity
          style={styles.button}
          onPress={onAction}
          activeOpacity={0.8}
        >
          <Text style={styles.buttonText}>{actionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: FashionDesignSystem.spacing.xxl,
  },
  icon: {
    marginBottom: FashionDesignSystem.spacing.lg,
    opacity: 0.5,
  },
  title: {
    ...FashionDesignSystem.typography.title,
    textAlign: 'center',
    marginBottom: FashionDesignSystem.spacing.sm,
  },
  description: {
    ...FashionDesignSystem.typography.body,
    textAlign: 'center',
    marginBottom: FashionDesignSystem.spacing.xl,
    lineHeight: 22,
    opacity: 0.8,
  },
  button: {
    ...FashionDesignSystem.components.primaryButton,
    marginTop: FashionDesignSystem.spacing.lg,
  },
  buttonText: {
    ...FashionDesignSystem.typography.button,
    color: FashionDesignSystem.colors.ctaText,
  },
});

export default EmptyState;
