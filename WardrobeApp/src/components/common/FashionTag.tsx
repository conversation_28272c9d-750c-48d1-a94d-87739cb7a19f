import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle, TextStyle } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

interface FashionTagProps {
  label: string;
  variant?: 'default' | 'selected' | 'category' | 'season';
  size?: 'small' | 'medium' | 'large';
  onPress?: () => void;
  onRemove?: () => void;
  icon?: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  removable?: boolean;
}

const FashionTag: React.FC<FashionTagProps> = ({
  label,
  variant = 'default',
  size = 'medium',
  onPress,
  onRemove,
  icon,
  style,
  textStyle,
  removable = false
}) => {
  const getTagStyle = () => {
    let baseStyle = styles.tag;
    
    switch (variant) {
      case 'selected':
        baseStyle = { ...baseStyle, ...styles.selectedTag };
        break;
      case 'category':
        baseStyle = { ...baseStyle, ...styles.categoryTag };
        break;
      case 'season':
        baseStyle = { ...baseStyle, ...styles.seasonTag };
        break;
    }
    
    switch (size) {
      case 'small':
        baseStyle = { ...baseStyle, ...styles.smallTag };
        break;
      case 'large':
        baseStyle = { ...baseStyle, ...styles.largeTag };
        break;
    }
    
    return baseStyle;
  };
  
  const getTextStyle = () => {
    let baseStyle = styles.tagText;
    
    switch (variant) {
      case 'selected':
        baseStyle = { ...baseStyle, ...styles.selectedTagText };
        break;
      case 'category':
        baseStyle = { ...baseStyle, ...styles.categoryTagText };
        break;
      case 'season':
        baseStyle = { ...baseStyle, ...styles.seasonTagText };
        break;
    }
    
    switch (size) {
      case 'small':
        baseStyle = { ...baseStyle, ...styles.smallTagText };
        break;
      case 'large':
        baseStyle = { ...baseStyle, ...styles.largeTagText };
        break;
    }
    
    return baseStyle;
  };

  const TagContent = () => (
    <View style={styles.tagContent}>
      {icon && (
        <Icon 
          name={icon} 
          size={size === 'small' ? 12 : size === 'large' ? 18 : 14} 
          color={getTextStyle().color}
          style={styles.tagIcon}
        />
      )}
      <Text style={[getTextStyle(), textStyle]}>{label}</Text>
      {removable && onRemove && (
        <TouchableOpacity onPress={onRemove} style={styles.removeButton}>
          <Icon 
            name="close" 
            size={size === 'small' ? 12 : size === 'large' ? 18 : 14} 
            color={getTextStyle().color}
          />
        </TouchableOpacity>
      )}
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        style={[getTagStyle(), style]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <TagContent />
      </TouchableOpacity>
    );
  }

  return (
    <View style={[getTagStyle(), style]}>
      <TagContent />
    </View>
  );
};

const styles = StyleSheet.create({
  tag: {
    ...FashionDesignSystem.components.tag,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // Variants
  selectedTag: {
    backgroundColor: FashionDesignSystem.colors.accent,
  },
  
  categoryTag: {
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
    borderWidth: 1,
    borderColor: FashionDesignSystem.colors.divider,
  },
  
  seasonTag: {
    backgroundColor: FashionDesignSystem.colors.highlight,
  },
  
  // Sizes
  smallTag: {
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 8,
  },
  
  largeTag: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  
  // Content
  tagContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  tagIcon: {
    marginRight: 4,
  },
  
  removeButton: {
    marginLeft: 4,
    padding: 2,
  },
  
  // Text Styles
  tagText: {
    ...FashionDesignSystem.typography.tag,
  },
  
  selectedTagText: {
    color: FashionDesignSystem.colors.background,
  },
  
  categoryTagText: {
    color: FashionDesignSystem.colors.primaryText,
  },
  
  seasonTagText: {
    color: FashionDesignSystem.colors.background,
  },
  
  smallTagText: {
    fontSize: 9,
  },
  
  largeTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default FashionTag;
