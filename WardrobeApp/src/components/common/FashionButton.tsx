import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, ActivityIndicator } from 'react-native';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

interface FashionButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

const FashionButton: React.FC<FashionButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false
}) => {
  const getButtonStyle = () => {
    let baseStyle = styles.button;
    
    switch (variant) {
      case 'primary':
        baseStyle = { ...baseStyle, ...styles.primaryButton };
        break;
      case 'secondary':
        baseStyle = { ...baseStyle, ...styles.secondaryButton };
        break;
      case 'ghost':
        baseStyle = { ...baseStyle, ...styles.ghostButton };
        break;
    }
    
    switch (size) {
      case 'small':
        baseStyle = { ...baseStyle, ...styles.smallButton };
        break;
      case 'large':
        baseStyle = { ...baseStyle, ...styles.largeButton };
        break;
    }
    
    if (fullWidth) {
      baseStyle = { ...baseStyle, ...styles.fullWidth };
    }
    
    if (disabled) {
      baseStyle = { ...baseStyle, ...styles.disabledButton };
    }
    
    return baseStyle;
  };
  
  const getTextStyle = () => {
    let baseStyle = styles.buttonText;
    
    switch (variant) {
      case 'primary':
        baseStyle = { ...baseStyle, ...styles.primaryButtonText };
        break;
      case 'secondary':
        baseStyle = { ...baseStyle, ...styles.secondaryButtonText };
        break;
      case 'ghost':
        baseStyle = { ...baseStyle, ...styles.ghostButtonText };
        break;
    }
    
    switch (size) {
      case 'small':
        baseStyle = { ...baseStyle, ...styles.smallButtonText };
        break;
      case 'large':
        baseStyle = { ...baseStyle, ...styles.largeButtonText };
        break;
    }
    
    if (disabled) {
      baseStyle = { ...baseStyle, ...styles.disabledButtonText };
    }
    
    return baseStyle;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={variant === 'primary' ? FashionDesignSystem.colors.ctaText : FashionDesignSystem.colors.accent} 
        />
      ) : (
        <Text style={[getTextStyle(), textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: FashionDesignSystem.borderRadius.button,
    paddingVertical: 12,
    paddingHorizontal: 24,
    minHeight: 48,
  },
  
  // Variants
  primaryButton: {
    ...FashionDesignSystem.components.primaryButton,
  },
  
  secondaryButton: {
    ...FashionDesignSystem.components.secondaryButton,
  },
  
  ghostButton: {
    ...FashionDesignSystem.components.ghostButton,
  },
  
  // Sizes
  smallButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    minHeight: 36,
  },
  
  largeButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    minHeight: 56,
  },
  
  // States
  disabledButton: {
    opacity: 0.5,
  },
  
  fullWidth: {
    width: '100%',
  },
  
  // Text Styles
  buttonText: {
    ...FashionDesignSystem.typography.button,
    textAlign: 'center',
  },
  
  primaryButtonText: {
    color: FashionDesignSystem.colors.ctaText,
  },
  
  secondaryButtonText: {
    color: FashionDesignSystem.colors.accent,
  },
  
  ghostButtonText: {
    color: FashionDesignSystem.colors.accent,
  },
  
  smallButtonText: {
    fontSize: 12,
  },
  
  largeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  
  disabledButtonText: {
    opacity: 0.7,
  },
});

export default FashionButton;
