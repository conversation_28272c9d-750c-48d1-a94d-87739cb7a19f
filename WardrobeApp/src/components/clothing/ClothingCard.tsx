import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { Text, Chip, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ClothingItem } from '../../types';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

interface ClothingCardProps {
  item: ClothingItem;
  onPress?: () => void;
  onFavorite?: () => void;
  showFavoriteButton?: boolean;
}

const ClothingCard: React.FC<ClothingCardProps> = ({
  item,
  onPress,
  onFavorite,
  showFavoriteButton = true
}) => {
  // Mock price for demonstration (in real app, this would come from item data)
  const price = item.price || '$120';

  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.9}>
      <View style={styles.imageContainer}>
        {item.images && item.images.length > 0 ? (
          <Image source={{ uri: item.images[0] }} style={styles.image} />
        ) : (
          <View style={styles.imagePlaceholder}>
            <Icon name="image" size={40} color={FashionDesignSystem.colors.lightText} />
          </View>
        )}

        {/* Favorite button positioned like in Figma */}
        {showFavoriteButton && (
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={onFavorite}
            activeOpacity={0.7}
          >
            <Icon
              name={item.isFavorite ? 'favorite' : 'favorite-border'}
              size={16}
              color={item.isFavorite ? FashionDesignSystem.colors.highlight : FashionDesignSystem.colors.lightText}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Product info - matching Figma layout */}
      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>
          {item.name || 'Reversible Angora Cardigan'}
        </Text>
        <Text style={styles.productPrice}>{price}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    ...FashionDesignSystem.components.productCard,
    flex: 1,
    marginHorizontal: FashionDesignSystem.spacing.productImageSpacing,
    marginBottom: FashionDesignSystem.spacing.lg,
  },

  imageContainer: {
    ...FashionDesignSystem.components.productImageContainer,
    position: 'relative',
    marginBottom: FashionDesignSystem.spacing.sm,
  },

  image: {
    width: '100%',
    height: '100%',
    borderRadius: FashionDesignSystem.borderRadius.md,
  },

  imagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: FashionDesignSystem.borderRadius.md,
  },

  favoriteButton: {
    position: 'absolute',
    top: FashionDesignSystem.spacing.sm,
    right: FashionDesignSystem.spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: FashionDesignSystem.borderRadius.circle,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    // Subtle shadow like in Figma
    shadowColor: FashionDesignSystem.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  productInfo: {
    alignItems: 'flex-start', // Left align like in Figma
  },

  productName: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.primaryText,
    fontWeight: '400',
    marginBottom: FashionDesignSystem.spacing.xs,
    lineHeight: 20,
  },

  productPrice: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.price,
    fontWeight: '600',
    fontSize: 16,
  },
});

export default ClothingCard;
