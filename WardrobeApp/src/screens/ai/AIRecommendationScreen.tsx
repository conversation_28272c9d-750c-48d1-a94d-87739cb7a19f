import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import {
  Text,
  Card,
  Chip,
  TextInput,
  Switch,
  Divider
} from 'react-native-paper';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList, RecommendationScene, WeatherInfo, ClothingItem } from '../../types';
import { DataService } from '../../services/DataService';
import { getSeason } from '../../utils/helpers';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';
import FashionButton from '../../components/common/FashionButton';

type AIRecommendationScreenRouteProp = RouteProp<RootStackParamList, 'AIRecommendation'>;
type AIRecommendationScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'AIRecommendation'>;

// 预设场景
const defaultScenes: RecommendationScene[] = [
  { id: 'work', name: '上班', icon: 'work', description: '正式商务场合' },
  { id: 'date', name: '约会', icon: 'favorite', description: '浪漫约会时光' },
  { id: 'travel', name: '旅行', icon: 'flight', description: '舒适出行装扮' },
  { id: 'home', name: '居家', icon: 'home', description: '轻松居家时光' },
  { id: 'sport', name: '运动', icon: 'fitness-center', description: '活力运动装备' },
  { id: 'party', name: '聚会', icon: 'celebration', description: '社交聚会场合' },
  { id: 'casual', name: '休闲', icon: 'weekend', description: '日常休闲穿搭' },
  { id: 'formal', name: '正式', icon: 'business-center', description: '重要正式场合' },
];

interface FormData {
  selectedScene: RecommendationScene | null;
  coreItem: ClothingItem | null;
  weather: WeatherInfo;
  useCurrentWeather: boolean;
  customCity: string;
}

const AIRecommendationScreen: React.FC = () => {
  const navigation = useNavigation<AIRecommendationScreenNavigationProp>();
  const route = useRoute<AIRecommendationScreenRouteProp>();
  const { itemId } = route.params || {};

  const [formData, setFormData] = useState<FormData>({
    selectedScene: null,
    coreItem: null,
    weather: {
      temperature: 20,
      condition: '晴天',
      humidity: 60,
      city: '北京',
    },
    useCurrentWeather: true,
    customCity: '',
  });
  const [loading, setLoading] = useState(false);
  const [loadingCoreItem, setLoadingCoreItem] = useState(false);

  useEffect(() => {
    if (itemId) {
      loadCoreItem();
    }
  }, [itemId]);

  const loadCoreItem = async () => {
    if (!itemId) return;

    setLoadingCoreItem(true);
    try {
      const item = await DataService.getClothingItemById(itemId);
      setFormData(prev => ({ ...prev, coreItem: item }));
    } catch (error) {
      console.error('Failed to load core item:', error);
    } finally {
      setLoadingCoreItem(false);
    }
  };

  const selectScene = (scene: RecommendationScene) => {
    setFormData(prev => ({ ...prev, selectedScene: scene }));
  };

  const updateWeather = (field: keyof WeatherInfo, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      weather: { ...prev.weather, [field]: value }
    }));
  };

  const getCurrentWeather = async () => {
    // 模拟获取当前天气
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockWeather: WeatherInfo = {
        temperature: Math.floor(Math.random() * 30) + 5,
        condition: ['晴天', '多云', '阴天', '小雨'][Math.floor(Math.random() * 4)],
        humidity: Math.floor(Math.random() * 40) + 40,
        city: formData.customCity || '北京',
      };

      setFormData(prev => ({ ...prev, weather: mockWeather }));
    } catch (error) {
      Alert.alert('获取天气失败', '请手动设置天气信息');
    } finally {
      setLoading(false);
    }
  };

  const generateRecommendation = async () => {
    if (!formData.selectedScene) {
      Alert.alert('提示', '请选择一个场景');
      return;
    }

    setLoading(true);
    try {
      // 模拟AI推荐生成
      await new Promise(resolve => setTimeout(resolve, 2000));

      // TODO: 导航到推荐详情页
      Alert.alert('推荐生成成功', '为您生成了3套搭配方案', [
        {
          text: '查看推荐',
          onPress: () => {
            // navigation.navigate('RecommendationDetail', { recommendationId: 'mock_id' });
            Alert.alert('提示', '推荐详情页开发中');
          },
        },
      ]);
    } catch (error) {
      Alert.alert('生成失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with title and back button - like wardrobe page */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Icon name="arrow-back" size={24} color={FashionDesignSystem.colors.primaryText} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>AI STYLING</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
        {/* 核心单品 */}
        {formData.coreItem && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.sectionTitle}>核心单品</Text>
              <View style={styles.coreItemContainer}>
                <View style={styles.coreItemImage}>
                  {formData.coreItem.images.length > 0 ? (
                    <Icon name="image" size={40} color="#6200EE" />
                  ) : (
                    <Icon name="checkroom" size={40} color="#ccc" />
                  )}
                </View>
                <View style={styles.coreItemInfo}>
                  <Text style={styles.coreItemName}>
                    {formData.coreItem.name || '未命名衣物'}
                  </Text>
                  <Text style={styles.coreItemDescription}>
                    以这件衣物为核心生成搭配
                  </Text>
                </View>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* 场景选择 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>选择场景</Text>
            <Text style={styles.sectionSubtitle}>
              {formData.coreItem ? '为核心单品选择合适的场景' : '选择您的穿搭场景'}
            </Text>

            <View style={styles.scenesContainer}>
              {defaultScenes.map((scene) => {
                const isSelected = formData.selectedScene?.id === scene.id;
                return (
                  <TouchableOpacity
                    key={scene.id}
                    style={[
                      styles.sceneCard,
                      isSelected && styles.sceneCardSelected
                    ]}
                    onPress={() => selectScene(scene)}
                    activeOpacity={0.7}
                  >
                    <View style={styles.sceneContent}>
                      <Icon
                        name={scene.icon}
                        size={32}
                        color={isSelected ? FashionDesignSystem.colors.accent : FashionDesignSystem.colors.secondaryText}
                      />
                      <Text style={[
                        styles.sceneName,
                        isSelected && styles.sceneNameSelected
                      ]}>
                        {scene.name}
                      </Text>
                      <Text style={styles.sceneDescription}>
                        {scene.description}
                      </Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </Card.Content>
        </Card>

        {/* 天气设置 */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>天气信息</Text>

            <View style={styles.weatherToggle}>
              <Text style={styles.weatherToggleText}>使用当前天气</Text>
              <Switch
                value={formData.useCurrentWeather}
                onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, useCurrentWeather: value }))
                }
              />
            </View>

            {formData.useCurrentWeather ? (
              <View style={styles.currentWeatherContainer}>
                <View style={styles.weatherInputContainer}>
                  <TextInput
                    label="城市"
                    value={formData.customCity}
                    onChangeText={(text) =>
                      setFormData(prev => ({ ...prev, customCity: text }))
                    }
                    mode="outlined"
                    style={styles.cityInput}
                    placeholder="输入城市名称"
                  />
                  <FashionButton
                    title="获取天气"
                    onPress={getCurrentWeather}
                    loading={loading}
                    disabled={loading}
                    variant="secondary"
                    size="small"
                    style={styles.getWeatherButton}
                  />
                </View>

                <View style={styles.weatherDisplay}>
                  <View style={styles.weatherItem}>
                    <Icon name="thermostat" size={20} color="#666" />
                    <Text style={styles.weatherText}>
                      {formData.weather.temperature}°C
                    </Text>
                  </View>
                  <View style={styles.weatherItem}>
                    <Icon name="wb-sunny" size={20} color="#666" />
                    <Text style={styles.weatherText}>
                      {formData.weather.condition}
                    </Text>
                  </View>
                  <View style={styles.weatherItem}>
                    <Icon name="water-drop" size={20} color="#666" />
                    <Text style={styles.weatherText}>
                      {formData.weather.humidity}%
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View style={styles.manualWeatherContainer}>
                <TextInput
                  label="温度 (°C)"
                  value={formData.weather.temperature.toString()}
                  onChangeText={(text) => updateWeather('temperature', parseInt(text) || 0)}
                  mode="outlined"
                  style={styles.weatherInput}
                  keyboardType="numeric"
                />
                <TextInput
                  label="天气状况"
                  value={formData.weather.condition}
                  onChangeText={(text) => updateWeather('condition', text)}
                  mode="outlined"
                  style={styles.weatherInput}
                  placeholder="如：晴天、多云、雨天"
                />
              </View>
            )}
          </Card.Content>
        </Card>

        {/* 生成推荐按钮 */}
        <FashionButton
          title={loading ? '正在生成推荐...' : 'AI生成推荐'}
          onPress={generateRecommendation}
          loading={loading}
          disabled={loading || !formData.selectedScene}
          variant="primary"
          size="large"
          fullWidth
          style={styles.generateButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  // Header styles - like wardrobe page
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: FashionDesignSystem.colors.border,
  },

  headerTitle: {
    ...FashionDesignSystem.typography.title,
    fontWeight: '700',
    letterSpacing: 1,
  },

  backButton: {
    padding: FashionDesignSystem.spacing.sm,
  },

  placeholder: {
    width: 40, // Same width as back button for centering
  },

  content: {
    flex: 1,
  },

  contentContainer: {
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    paddingBottom: 100,
  },
  card: {
    ...FashionDesignSystem.components.card,
    marginBottom: FashionDesignSystem.spacing.lg,
  },
  sectionTitle: {
    ...FashionDesignSystem.typography.subtitle,
    marginBottom: FashionDesignSystem.spacing.sm,
  },
  sectionSubtitle: {
    ...FashionDesignSystem.typography.body,
    color: FashionDesignSystem.colors.secondaryText,
    marginBottom: FashionDesignSystem.spacing.lg,
  },
  coreItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
    borderRadius: FashionDesignSystem.borderRadius.md,
  },
  coreItemImage: {
    width: 60,
    height: 60,
    backgroundColor: FashionDesignSystem.colors.surface,
    borderRadius: FashionDesignSystem.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: FashionDesignSystem.spacing.lg,
    ...FashionDesignSystem.shadows.light,
  },
  coreItemInfo: {
    flex: 1,
  },
  coreItemName: {
    ...FashionDesignSystem.typography.body,
    fontWeight: '500',
    marginBottom: FashionDesignSystem.spacing.xs,
  },
  coreItemDescription: {
    ...FashionDesignSystem.typography.caption,
  },
  scenesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: FashionDesignSystem.spacing.md,
  },
  sceneCard: {
    width: '48%',
    padding: FashionDesignSystem.spacing.lg,
    borderRadius: FashionDesignSystem.borderRadius.card,
    borderWidth: 1,
    borderColor: FashionDesignSystem.colors.divider,
    backgroundColor: FashionDesignSystem.colors.surface,
    ...FashionDesignSystem.shadows.light,
  },
  sceneCardSelected: {
    borderColor: FashionDesignSystem.colors.accent,
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
  },
  sceneContent: {
    alignItems: 'center',
  },
  sceneName: {
    ...FashionDesignSystem.typography.body,
    fontWeight: '500',
    marginTop: FashionDesignSystem.spacing.sm,
    marginBottom: FashionDesignSystem.spacing.xs,
    textAlign: 'center',
  },
  sceneNameSelected: {
    color: FashionDesignSystem.colors.accent,
  },
  sceneDescription: {
    ...FashionDesignSystem.typography.caption,
    textAlign: 'center',
  },
  weatherToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: FashionDesignSystem.spacing.lg,
  },
  weatherToggleText: {
    ...FashionDesignSystem.typography.body,
  },
  currentWeatherContainer: {
    gap: FashionDesignSystem.spacing.lg,
  },
  weatherInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: FashionDesignSystem.spacing.md,
  },
  cityInput: {
    flex: 1,
  },
  getWeatherButton: {
    marginTop: FashionDesignSystem.spacing.sm,
  },
  weatherDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
    borderRadius: FashionDesignSystem.borderRadius.md,
  },
  weatherItem: {
    alignItems: 'center',
    gap: FashionDesignSystem.spacing.xs,
  },
  weatherText: {
    ...FashionDesignSystem.typography.body,
    fontWeight: '500',
  },
  manualWeatherContainer: {
    gap: FashionDesignSystem.spacing.lg,
  },
  weatherInput: {
    marginBottom: FashionDesignSystem.spacing.sm,
  },
  generateButton: {
    marginTop: FashionDesignSystem.spacing.lg,
    marginBottom: FashionDesignSystem.spacing.xxl,
  },
});

export default AIRecommendationScreen;
