import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, SafeAreaView } from 'react-native';
import {
  Text,
  Searchbar,
  Appbar,
} from 'react-native-paper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootStackParamList, ClothingItem, ClothingCategory } from '../../types';
import { ClothingStorage } from '../../utils/storage';
import ClothingCard from '../../components/clothing/ClothingCard';
import EmptyState from '../../components/common/EmptyState';
import CategoryTabs from '../../components/navigation/CategoryTabs';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';

type WardrobeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Main'>;

// 预设分类
const defaultCategories = [
  { id: '1', name: '上衣', icon: 'checkroom', isCustom: false, order: 1 },
  { id: '2', name: '裤子', icon: 'straighten', isCustom: false, order: 2 },
  { id: '3', name: '裙子', icon: 'woman', isCustom: false, order: 3 },
  { id: '4', name: '鞋子', icon: 'sports-tennis', isCustom: false, order: 4 },
  { id: '5', name: '袜子', icon: 'socks', isCustom: false, order: 5 },
  { id: '6', name: '帽子', icon: 'sports-baseball', isCustom: false, order: 6 },
  { id: '7', name: '眼镜', icon: 'visibility', isCustom: false, order: 7 },
  { id: '8', name: '项链', icon: 'favorite', isCustom: false, order: 8 },
  { id: '9', name: '耳环', icon: 'hearing', isCustom: false, order: 9 },
  { id: '10', name: '手链', icon: 'watch', isCustom: false, order: 10 },
  { id: '11', name: '文胸', icon: 'favorite-border', isCustom: false, order: 11 },
  { id: '12', name: '内裤', icon: 'favorite-border', isCustom: false, order: 12 },
];

const WardrobeScreen: React.FC = () => {
  const navigation = useNavigation<WardrobeScreenNavigationProp>();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [loading, setLoading] = useState(true);

  // 计算每个分类的衣物数量
  const categoriesWithCount = defaultCategories.map(category => ({
    ...category,
    count: clothingItems.filter(item => item.categoryId === category.id).length,
  }));

  // 加载衣物数据
  const loadClothingItems = async () => {
    try {
      const items = await ClothingStorage.getClothingItems();
      setClothingItems(items);
    } catch (error) {
      console.error('Failed to load clothing items:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面聚焦时重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      loadClothingItems();
    }, [])
  );

  // 筛选衣物
  const filteredItems = clothingItems.filter(item => {
    const matchesCategory = !selectedCategory || item.categoryId === selectedCategory;
    const matchesSearch = !searchQuery ||
      (item.name && item.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      item.tags.some(tag => tag.name.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });



  const handleItemPress = (item: ClothingItem) => {
    navigation.navigate('ItemDetail', { itemId: item.id });
  };

  const handleFavoritePress = async (item: ClothingItem) => {
    try {
      await ClothingStorage.updateClothingItem(item.id, { isFavorite: !item.isFavorite });
      loadClothingItems(); // 重新加载数据
    } catch (error) {
      console.error('Failed to update favorite:', error);
    }
  };

  const renderClothingItem = ({ item }: { item: ClothingItem }) => (
    <ClothingCard
      item={item}
      onPress={() => handleItemPress(item)}
      onFavorite={() => handleFavoritePress(item)}
      showFavoriteButton={true}
    />
  );

  const handleAddClothing = () => {
    navigation.navigate('Upload' as never);
  };

  // Categories for the top navigation (like in Figma)
  const categories = [
    { id: 'all', name: 'All' },
    { id: 'apparel', name: 'Apparel' },
    { id: 'dress', name: 'Dress' },
    { id: 'tshirt', name: 'Tshirt' },
    { id: 'bag', name: 'Bag' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with title and search - like Figma */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>OUR FASHION</Text>
        <TouchableOpacity style={styles.searchButton}>
          <Icon name="search" size={24} color={FashionDesignSystem.colors.primaryText} />
        </TouchableOpacity>
      </View>

      {/* Category Navigation */}
      <CategoryTabs
        categories={categories}
        activeCategory={selectedCategory || 'all'}
        onCategoryChange={(categoryId) => setSelectedCategory(categoryId === 'all' ? null : categoryId)}
      />

      {/* Products Grid */}
      <View style={styles.productsContainer}>
        {filteredItems.length === 0 ? (
          <EmptyState
            icon="checkroom"
            title={loading ? "Loading..." : "No Products"}
            description={loading ? "Loading your wardrobe..." :
              selectedCategory ? "No items in this category" : "Add your first clothing item"}
            actionText={!loading && !selectedCategory ? "Add Item" : undefined}
            onAction={!loading && !selectedCategory ? handleAddClothing : undefined}
          />
        ) : (
          <FlatList
            data={filteredItems}
            renderItem={renderClothingItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.productsList}
            columnWrapperStyle={styles.productRow}
          />
        )}
      </View>

      {/* Floating Add Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={handleAddClothing}
        activeOpacity={0.8}
      >
        <Icon name="add" size={24} color={FashionDesignSystem.colors.background} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  // Header styles - like Figma
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    backgroundColor: FashionDesignSystem.colors.background,
  },

  headerTitle: {
    ...FashionDesignSystem.typography.title,
    fontWeight: '700',
    letterSpacing: 1,
  },

  searchButton: {
    padding: FashionDesignSystem.spacing.sm,
  },

  // Products container
  productsContainer: {
    flex: 1,
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
  },

  productsList: {
    paddingBottom: 100, // Space for FAB
  },

  productRow: {
    justifyContent: 'space-between',
    paddingHorizontal: FashionDesignSystem.spacing.xs,
  },
  // Floating Action Button
  fab: {
    ...FashionDesignSystem.components.fab,
  },
});

export default WardrobeScreen;
