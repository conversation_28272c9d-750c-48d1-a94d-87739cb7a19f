import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Image, TouchableOpacity, SafeAreaView } from 'react-native';
import { Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList, ClothingItem } from '../../types';
import { FashionDesignSystem } from '../../styles/FashionDesignSystem';
import FashionButton from '../../components/common/FashionButton';

type ProductDetailScreenRouteProp = RouteProp<RootStackParamList, 'ItemDetail'>;
type ProductDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ItemDetail'>;

const ProductDetailScreen: React.FC = () => {
  const navigation = useNavigation<ProductDetailScreenNavigationProp>();
  const route = useRoute<ProductDetailScreenRouteProp>();
  
  // Mock data - in real app, fetch based on route.params.itemId
  const [selectedColor, setSelectedColor] = useState('black');
  const [selectedSize, setSelectedSize] = useState('M');
  const [isFavorite, setIsFavorite] = useState(false);
  
  // Mock product data based on Figma design
  const product = {
    name: 'Oblong Bag',
    price: '$120.00',
    images: [
      'https://example.com/bag1.jpg', // In real app, use actual images
    ],
    colors: [
      { id: 'black', name: 'Black', color: '#000000' },
      { id: 'white', name: 'White', color: '#FFFFFF' },
      { id: 'brown', name: 'Brown', color: '#8B4513' },
    ],
    sizes: ['XS', 'S', 'M', 'L', 'XL'],
    description: 'A minimalist oblong bag perfect for everyday use.',
  };

  const handleAddToCart = () => {
    // Add to cart logic
    console.log('Added to cart:', { product, selectedColor, selectedSize });
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Icon name="arrow-back" size={24} color={FashionDesignSystem.colors.primaryText} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{product.name}</Text>
        <TouchableOpacity onPress={toggleFavorite} style={styles.favoriteButton}>
          <Icon 
            name={isFavorite ? 'favorite' : 'favorite-border'} 
            size={24} 
            color={isFavorite ? FashionDesignSystem.colors.highlight : FashionDesignSystem.colors.primaryText} 
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Image */}
        <View style={styles.imageContainer}>
          <View style={styles.imagePlaceholder}>
            <Icon name="image" size={80} color={FashionDesignSystem.colors.lightText} />
          </View>
          {/* Image indicators */}
          <View style={styles.imageIndicators}>
            <View style={[styles.indicator, styles.activeIndicator]} />
            <View style={styles.indicator} />
            <View style={styles.indicator} />
          </View>
        </View>

        {/* Product Info */}
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productPrice}>{product.price}</Text>
        </View>

        {/* Color Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>color</Text>
          <View style={styles.colorOptions}>
            {product.colors.map((color) => (
              <TouchableOpacity
                key={color.id}
                style={[
                  styles.colorOption,
                  { backgroundColor: color.color },
                  selectedColor === color.id && styles.selectedColorOption
                ]}
                onPress={() => setSelectedColor(color.id)}
              />
            ))}
          </View>
        </View>

        {/* Size Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>size</Text>
          <View style={styles.sizeOptions}>
            {product.sizes.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.sizeOption,
                  selectedSize === size && styles.selectedSizeOption
                ]}
                onPress={() => setSelectedSize(size)}
              >
                <Text style={[
                  styles.sizeText,
                  selectedSize === size && styles.selectedSizeText
                ]}>
                  {size}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Add to Cart Button */}
      <View style={styles.bottomSection}>
        <FashionButton
          title="Add to Cart"
          onPress={handleAddToCart}
          variant="primary"
          size="large"
          fullWidth
          style={styles.addToCartButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FashionDesignSystem.colors.background,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: FashionDesignSystem.colors.border,
  },
  
  backButton: {
    padding: FashionDesignSystem.spacing.sm,
  },
  
  headerTitle: {
    ...FashionDesignSystem.typography.subtitle,
    fontWeight: '500',
  },
  
  favoriteButton: {
    padding: FashionDesignSystem.spacing.sm,
  },
  
  content: {
    flex: 1,
  },
  
  imageContainer: {
    aspectRatio: 0.8,
    backgroundColor: FashionDesignSystem.colors.surfaceVariant,
    marginHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    marginTop: FashionDesignSystem.spacing.lg,
    borderRadius: FashionDesignSystem.borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  
  imagePlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  imageIndicators: {
    position: 'absolute',
    bottom: FashionDesignSystem.spacing.lg,
    flexDirection: 'row',
    gap: FashionDesignSystem.spacing.sm,
  },
  
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: FashionDesignSystem.colors.lightText,
  },
  
  activeIndicator: {
    backgroundColor: FashionDesignSystem.colors.primaryText,
  },
  
  productInfo: {
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.xl,
    alignItems: 'center',
  },
  
  productName: {
    ...FashionDesignSystem.typography.title,
    marginBottom: FashionDesignSystem.spacing.sm,
  },
  
  productPrice: {
    ...FashionDesignSystem.typography.subtitle,
    fontWeight: '600',
  },
  
  section: {
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    marginBottom: FashionDesignSystem.spacing.xl,
  },
  
  sectionTitle: {
    ...FashionDesignSystem.typography.body,
    marginBottom: FashionDesignSystem.spacing.md,
    color: FashionDesignSystem.colors.secondaryText,
  },
  
  colorOptions: {
    flexDirection: 'row',
    gap: FashionDesignSystem.spacing.md,
  },
  
  colorOption: {
    ...FashionDesignSystem.components.colorSelector,
  },
  
  selectedColorOption: {
    ...FashionDesignSystem.components.colorSelectorSelected,
  },
  
  sizeOptions: {
    flexDirection: 'row',
    gap: FashionDesignSystem.spacing.sm,
  },
  
  sizeOption: {
    ...FashionDesignSystem.components.sizeSelector,
  },
  
  selectedSizeOption: {
    ...FashionDesignSystem.components.sizeSelectorSelected,
  },
  
  sizeText: {
    ...FashionDesignSystem.typography.body,
    fontWeight: '500',
  },
  
  selectedSizeText: {
    color: FashionDesignSystem.colors.background,
  },
  
  bottomSection: {
    paddingHorizontal: FashionDesignSystem.spacing.screenPaddingHorizontal,
    paddingVertical: FashionDesignSystem.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: FashionDesignSystem.colors.border,
  },
  
  addToCartButton: {
    ...FashionDesignSystem.components.addToCartButton,
  },
});

export default ProductDetailScreen;
