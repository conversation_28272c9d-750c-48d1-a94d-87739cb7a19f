<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>衣柜管理应用 - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: #6200EE;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-header {
            height: 56px;
            background: #6200EE;
            display: flex;
            align-items: center;
            padding: 0 16px;
            color: white;
            font-size: 20px;
            font-weight: 500;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
        }
        
        .splash-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .splash-screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
        }

        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 64px;
            margin-bottom: 24px;
            animation: iosFloat 3s ease-in-out infinite;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            z-index: 1;
        }

        .app-name {
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: 0.37px;
            animation: slideUp 0.8s ease-out 0.3s both;
            z-index: 1;
        }

        .tagline {
            font-size: 20px;
            opacity: 0.9;
            font-weight: 400;
            letter-spacing: 0.38px;
            margin-bottom: 60px;
            animation: slideUp 0.8s ease-out 0.5s both;
            z-index: 1;
        }
        
        .loading {
            position: absolute;
            bottom: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 1;
        }

        .loading-dots {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.7);
            animation: loadingPulse 1.5s ease-in-out infinite;
        }

        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        .version {
            font-size: 12px;
            opacity: 0.6;
            font-weight: 500;
        }

        @keyframes iosFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
            }
            50% {
                transform: translateY(-10px) scale(1.05);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes loadingPulse {
            0%, 100% {
                opacity: 0.4;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        .login-screen {
            padding: 24px;
            display: none;
            background: #F2F2F7;
        }

        .login-header {
            text-align: center;
            margin: 40px 0 48px 0;
        }

        .login-title {
            font-size: 34px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #000;
            letter-spacing: 0.37px;
        }

        .login-subtitle {
            color: #3C3C43;
            font-size: 17px;
            font-weight: 400;
            opacity: 0.8;
        }
        
        .login-form {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .login-tabs {
            display: flex;
            background: #f0f0f0;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 20px;
        }
        
        .login-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .login-tab.active {
            background: #6200EE;
            color: white;
        }
        
        .input-group {
            margin-bottom: 16px;
        }
        
        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #6200EE;
        }
        
        .login-btn {
            width: 100%;
            padding: 16px;
            background: #6200EE;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 20px;
            transition: background 0.2s;
        }
        
        .login-btn:hover {
            background: #3700B3;
        }
        
        .wardrobe-screen {
            display: none;
            height: 100%;
        }
        
        .wardrobe-content {
            display: flex;
            height: calc(100% - 56px);
        }
        
        .category-sidebar {
            width: 120px;
            background: white;
            border-right: 1px solid #e0e0e0;
            padding: 16px 8px;
        }
        
        .category-item {
            text-align: center;
            padding: 12px 8px;
            margin-bottom: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .category-item:hover {
            background: #f0f0f0;
        }
        
        .category-item.active {
            background: #e8f5e8;
        }
        
        .category-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .category-name {
            font-size: 12px;
            font-weight: 500;
        }
        
        .items-area {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        
        .items-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .clothing-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card-image {
            height: 120px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: #ccc;
        }
        
        .card-content {
            padding: 12px;
        }
        
        .card-title {
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .card-tags {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }
        
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
        }
        
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.2s;
        }
        
        .nav-item:hover {
            background: #f0f0f0;
        }
        
        .nav-item.active {
            color: #6200EE;
        }
        
        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .controls {
            text-align: center;
            margin: 20px;
        }
        
        .control-btn {
            margin: 0 5px;
            padding: 8px 16px;
            background: #6200EE;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }

        /* 上传页面样式 */
        .upload-screen, .ai-screen, .profile-screen {
            display: none;
            height: 100%;
        }

        .upload-content, .ai-content, .profile-content {
            padding: 16px;
            height: calc(100% - 100px);
            overflow-y: auto;
        }

        .upload-section, .ai-section {
            margin-bottom: 20px;
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .upload-section h3, .ai-section h3 {
            margin: 0 0 12px 0;
            font-size: 16px;
            color: #333;
        }

        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .add-image-btn {
            color: #666;
            font-size: 14px;
        }

        .upload-input, .upload-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 12px;
            font-size: 14px;
        }

        .upload-textarea {
            height: 60px;
            resize: none;
        }

        .category-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
        }

        .tag-types {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .tag-type {
            flex: 1;
            padding: 8px;
            border: none;
            border-radius: 4px;
            background: #f0f0f0;
            cursor: pointer;
        }

        .tag-type.active {
            background: #6200EE;
            color: white;
        }

        .tags-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag {
            padding: 6px 12px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
        }

        .save-btn, .generate-btn {
            width: 100%;
            padding: 16px;
            background: #6200EE;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
        }

        /* AI推荐页面样式 */
        .scenes-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .scene-card {
            padding: 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            background: white;
        }

        .scene-card.active {
            border-color: #6200EE;
            background: #f3e5f5;
        }

        .scene-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .scene-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .scene-desc {
            font-size: 12px;
            color: #666;
        }

        .weather-info {
            display: flex;
            justify-content: space-around;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .weather-item {
            text-align: center;
            font-size: 14px;
        }

        /* 个人中心样式 */
        .user-card {
            display: flex;
            align-items: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            background: #6200EE;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-right: 16px;
        }

        .user-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .user-email {
            color: #666;
            margin-bottom: 4px;
        }

        .user-join {
            font-size: 12px;
            color: #999;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }

        .stats-card h3 {
            margin: 0 0 16px 0;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #6200EE;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .menu-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-icon {
            font-size: 20px;
            margin-right: 16px;
        }

        .menu-text {
            flex: 1;
        }

        .menu-title {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .menu-desc {
            font-size: 12px;
            color: #666;
        }

        .menu-arrow {
            font-size: 18px;
            color: #666;
        }

        .logout-btn {
            width: 100%;
            padding: 12px;
            background: white;
            color: #d32f2f;
            border: 1px solid #d32f2f;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="phone-frame">
        <div class="screen">
            <!-- 启动页面 -->
            <div id="splash" class="splash-screen">
                <div class="logo">👗</div>
                <div class="app-name">衣柜管家</div>
                <div class="tagline">智能管理您的时尚生活</div>
                <div class="loading">
                    <div class="loading-dots">
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                        <div class="loading-dot"></div>
                    </div>
                    <div class="version">Version 1.0.0</div>
                </div>
            </div>
            
            <!-- 登录页面 -->
            <div id="login" class="login-screen">
                <div class="login-header">
                    <div class="login-title">欢迎回来</div>
                    <div class="login-subtitle">登录您的衣柜管理账户</div>
                </div>
                
                <div class="login-form">
                    <div class="login-tabs">
                        <div class="login-tab active">手机号</div>
                        <div class="login-tab">邮箱</div>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">手机号</label>
                        <input type="tel" class="input-field" placeholder="请输入手机号">
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">密码</label>
                        <input type="password" class="input-field" placeholder="请输入密码">
                    </div>
                    
                    <button class="login-btn" onclick="showWardrobe()">登录</button>
                </div>
            </div>
            
            <!-- 衣柜主页面 -->
            <div id="wardrobe" class="wardrobe-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>🔋 100%</span>
                </div>
                
                <div class="app-header">
                    我的衣柜
                </div>
                
                <div class="wardrobe-content">
                    <div class="category-sidebar">
                        <div class="category-item active">
                            <div class="category-icon">👔</div>
                            <div class="category-name">上衣</div>
                        </div>
                        <div class="category-item">
                            <div class="category-icon">👖</div>
                            <div class="category-name">裤子</div>
                        </div>
                        <div class="category-item">
                            <div class="category-icon">👗</div>
                            <div class="category-name">裙子</div>
                        </div>
                        <div class="category-item">
                            <div class="category-icon">👟</div>
                            <div class="category-name">鞋子</div>
                        </div>
                        <div class="category-item">
                            <div class="category-icon">🧦</div>
                            <div class="category-name">袜子</div>
                        </div>
                        <div class="category-item">
                            <div class="category-icon">🎩</div>
                            <div class="category-name">帽子</div>
                        </div>
                    </div>
                    
                    <div class="items-area">
                        <div class="items-grid">
                            <div class="clothing-card">
                                <div class="card-image">👔</div>
                                <div class="card-content">
                                    <div class="card-title">白色衬衫</div>
                                    <div class="card-tags">
                                        <span class="tag">商务</span>
                                        <span class="tag">春夏</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="clothing-card">
                                <div class="card-image">👕</div>
                                <div class="card-content">
                                    <div class="card-title">休闲T恤</div>
                                    <div class="card-tags">
                                        <span class="tag">休闲</span>
                                        <span class="tag">夏季</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="clothing-card">
                                <div class="card-image">🧥</div>
                                <div class="card-content">
                                    <div class="card-title">西装外套</div>
                                    <div class="card-tags">
                                        <span class="tag">正式</span>
                                        <span class="tag">秋冬</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="clothing-card">
                                <div class="card-image">👚</div>
                                <div class="card-content">
                                    <div class="card-title">针织衫</div>
                                    <div class="card-tags">
                                        <span class="tag">温暖</span>
                                        <span class="tag">冬季</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bottom-nav">
                    <div class="nav-item active">
                        <div class="nav-icon">👗</div>
                        <div class="nav-label">衣柜</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">❤️</div>
                        <div class="nav-label">收藏</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">➕</div>
                        <div class="nav-label">上传</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">🤖</div>
                        <div class="nav-label">AI推荐</div>
                    </div>
                    <div class="nav-item">
                        <div class="nav-icon">👤</div>
                        <div class="nav-label">我的</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 上传页面 -->
        <div id="upload" class="upload-screen" style="display: none;">
            <div class="status-bar">
                <span>9:41</span>
                <span>🔋 100%</span>
            </div>

            <div class="app-header">
                <span onclick="showWardrobe()" style="cursor: pointer;">←</span>
                上传衣物
                <span>✓</span>
            </div>

            <div class="upload-content">
                <div class="upload-section">
                    <h3>📷 衣物图片</h3>
                    <div class="image-upload-area">
                        <div class="add-image-btn">
                            <div>📷</div>
                            <div>添加图片</div>
                        </div>
                    </div>
                </div>

                <div class="upload-section">
                    <h3>📝 基本信息</h3>
                    <input type="text" placeholder="衣物名称（可选）" class="upload-input">
                    <textarea placeholder="描述（可选）" class="upload-textarea"></textarea>
                </div>

                <div class="upload-section">
                    <h3>📂 选择分类</h3>
                    <div class="category-selector">
                        <span>请选择分类</span>
                        <span>▼</span>
                    </div>
                </div>

                <div class="upload-section">
                    <h3>🏷️ 添加标签</h3>
                    <div class="tag-types">
                        <button class="tag-type active">季节</button>
                        <button class="tag-type">风格</button>
                        <button class="tag-type">材质</button>
                    </div>
                    <div class="tags-grid">
                        <span class="tag">春季</span>
                        <span class="tag">夏季</span>
                        <span class="tag">秋季</span>
                        <span class="tag">冬季</span>
                    </div>
                </div>

                <button class="save-btn">保存到衣柜</button>
            </div>
        </div>

        <!-- AI推荐页面 -->
        <div id="ai" class="ai-screen" style="display: none;">
            <div class="status-bar">
                <span>9:41</span>
                <span>🔋 100%</span>
            </div>

            <div class="app-header">
                <span onclick="showWardrobe()" style="cursor: pointer;">←</span>
                AI智能推荐
            </div>

            <div class="ai-content">
                <div class="ai-section">
                    <h3>🎯 选择场景</h3>
                    <div class="scenes-grid">
                        <div class="scene-card">
                            <div class="scene-icon">💼</div>
                            <div class="scene-name">上班</div>
                            <div class="scene-desc">正式商务场合</div>
                        </div>
                        <div class="scene-card active">
                            <div class="scene-icon">💕</div>
                            <div class="scene-name">约会</div>
                            <div class="scene-desc">浪漫约会时光</div>
                        </div>
                        <div class="scene-card">
                            <div class="scene-icon">✈️</div>
                            <div class="scene-name">旅行</div>
                            <div class="scene-desc">舒适出行装扮</div>
                        </div>
                        <div class="scene-card">
                            <div class="scene-icon">🏠</div>
                            <div class="scene-name">居家</div>
                            <div class="scene-desc">轻松居家时光</div>
                        </div>
                    </div>
                </div>

                <div class="ai-section">
                    <h3>🌤️ 天气信息</h3>
                    <div class="weather-info">
                        <div class="weather-item">
                            <span>🌡️</span>
                            <span>22°C</span>
                        </div>
                        <div class="weather-item">
                            <span>☀️</span>
                            <span>晴天</span>
                        </div>
                        <div class="weather-item">
                            <span>💧</span>
                            <span>65%</span>
                        </div>
                    </div>
                </div>

                <button class="generate-btn">🤖 AI生成推荐</button>
            </div>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile" class="profile-screen" style="display: none;">
            <div class="status-bar">
                <span>9:41</span>
                <span>🔋 100%</span>
            </div>

            <div class="app-header">
                个人中心
            </div>

            <div class="profile-content">
                <div class="user-card">
                    <div class="user-avatar">👤</div>
                    <div class="user-info">
                        <div class="user-name">时尚达人</div>
                        <div class="user-email"><EMAIL></div>
                        <div class="user-join">加入时间：2024-01-15</div>
                    </div>
                </div>

                <div class="stats-card">
                    <h3>我的衣橱</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">24</div>
                            <div class="stat-label">总衣物</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">8</div>
                            <div class="stat-label">收藏单品</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">12</div>
                            <div class="stat-label">推荐记录</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">6</div>
                            <div class="stat-label">分类数量</div>
                        </div>
                    </div>
                </div>

                <div class="menu-card">
                    <div class="menu-item">
                        <span class="menu-icon">❤️</span>
                        <div class="menu-text">
                            <div class="menu-title">收藏夹</div>
                            <div class="menu-desc">8 件单品，3 套搭配</div>
                        </div>
                        <span class="menu-arrow">›</span>
                    </div>
                    <div class="menu-item">
                        <span class="menu-icon">📚</span>
                        <div class="menu-text">
                            <div class="menu-title">搭配历史</div>
                            <div class="menu-desc">查看历史搭配记录</div>
                        </div>
                        <span class="menu-arrow">›</span>
                    </div>
                    <div class="menu-item">
                        <span class="menu-icon">🤖</span>
                        <div class="menu-text">
                            <div class="menu-title">AI补全衣橱</div>
                            <div class="menu-desc">分析衣橱并推荐缺失单品</div>
                        </div>
                        <span class="menu-arrow">›</span>
                    </div>
                    <div class="menu-item">
                        <span class="menu-icon">⚙️</span>
                        <div class="menu-text">
                            <div class="menu-title">设置</div>
                            <div class="menu-desc">个人设置和偏好</div>
                        </div>
                        <span class="menu-arrow">›</span>
                    </div>
                </div>

                <button class="logout-btn">退出登录</button>
            </div>
        </div>
    </div>
    
    <div class="controls">
        <button class="control-btn" onclick="showSplash()">启动页</button>
        <button class="control-btn" onclick="showLogin()">登录页</button>
        <button class="control-btn" onclick="showWardrobe()">衣柜页</button>
        <button class="control-btn" onclick="showUpload()">上传页</button>
        <button class="control-btn" onclick="showAI()">AI推荐</button>
        <button class="control-btn" onclick="showProfile()">个人中心</button>
    </div>
    
    <script>
        function hideAllScreens() {
            document.getElementById('splash').style.display = 'none';
            document.getElementById('login').style.display = 'none';
            document.getElementById('wardrobe').style.display = 'none';
            document.getElementById('upload').style.display = 'none';
            document.getElementById('ai').style.display = 'none';
            document.getElementById('profile').style.display = 'none';
        }

        function showSplash() {
            hideAllScreens();
            document.getElementById('splash').style.display = 'flex';
        }

        function showLogin() {
            hideAllScreens();
            document.getElementById('login').style.display = 'block';
        }

        function showWardrobe() {
            hideAllScreens();
            document.getElementById('wardrobe').style.display = 'block';
        }

        function showUpload() {
            hideAllScreens();
            document.getElementById('upload').style.display = 'block';
        }

        function showAI() {
            hideAllScreens();
            document.getElementById('ai').style.display = 'block';
        }

        function showProfile() {
            hideAllScreens();
            document.getElementById('profile').style.display = 'block';
        }
        
        // 自动演示
        setTimeout(() => {
            showLogin();
        }, 3000);
    </script>
</body>
</html>
